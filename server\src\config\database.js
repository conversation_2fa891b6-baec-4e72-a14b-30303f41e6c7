const { Sequelize } = require('sequelize');

let sequelize;

const connectDB = async () => {
  try {
    // Create Sequelize instance
    sequelize = new Sequelize(process.env.DATABASE_URL || {
      database: process.env.DB_NAME || 'ride_hailing',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      dialect: 'postgres',
      logging: process.env.NODE_ENV === 'development' ? console.log : false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    });

    // Test the connection
    await sequelize.authenticate();
    console.log('Database connection has been established successfully.');

    // Sync models in development
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('Database models synchronized.');
    }

    return sequelize;
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    throw error;
  }
};

const getSequelize = () => {
  if (!sequelize) {
    throw new Error('Database not initialized. Call connectDB first.');
  }
  return sequelize;
};

module.exports = {
  connectDB,
  getSequelize,
  sequelize
};
