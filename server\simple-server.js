const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors({
  origin: "http://localhost:5173",
  credentials: true
}));

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Ride-hailing API is running (simplified version)',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mock API Routes
app.post('/api/auth/login', (req, res) => {
  const { email, password, role } = req.body;
  
  // Mock authentication - accept any credentials for demo
  if (email && password) {
    res.json({
      success: true,
      data: {
        user: {
          id: '1',
          firstName: 'Demo',
          lastName: 'User',
          email: email,
          role: role || 'user'
        },
        token: 'demo-jwt-token-' + Date.now()
      }
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { firstName, lastName, email, password, role } = req.body;
  
  // Mock registration - accept any data for demo
  if (firstName && lastName && email && password) {
    res.json({
      success: true,
      data: {
        user: {
          id: '1',
          firstName,
          lastName,
          email,
          role: role || 'user'
        },
        token: 'demo-jwt-token-' + Date.now()
      }
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }
});

app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

app.get('/api/auth/verify', (req, res) => {
  res.json({
    success: true,
    data: {
      user: {
        id: '1',
        firstName: 'Demo',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'user'
      }
    }
  });
});

// Mock user routes
app.get('/api/users/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      id: '1',
      firstName: 'Demo',
      lastName: 'User',
      email: '<EMAIL>',
      role: 'user'
    }
  });
});

// Mock ride routes
app.post('/api/rides/request', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 'ride-' + Date.now(),
      status: 'requested',
      message: 'Ride requested successfully'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simplified server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 API URL: http://localhost:${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
});
