const jwt = require('jsonwebtoken');
const { cache } = require('../config/redis');

// Store active connections
const activeConnections = new Map();
const driverLocations = new Map();

const socketHandler = (io) => {
  // Socket authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      socket.userId = decoded.id;
      socket.userRole = decoded.role;
      
      next();
    } catch (error) {
      next(new Error('Authentication error: Invalid token'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User ${socket.userId} (${socket.userRole}) connected`);
    
    // Store active connection
    activeConnections.set(socket.userId, {
      socketId: socket.id,
      role: socket.userRole,
      connectedAt: new Date()
    });

    // Join user to their specific room
    socket.join(`user_${socket.userId}`);
    
    // Join role-specific rooms
    socket.join(`role_${socket.userRole}`);

    // Handle driver location updates
    socket.on('driver_location_update', async (data) => {
      if (socket.userRole !== 'driver') {
        return socket.emit('error', { message: 'Unauthorized: Only drivers can update location' });
      }

      const { latitude, longitude, heading, speed } = data;
      
      if (!latitude || !longitude) {
        return socket.emit('error', { message: 'Invalid location data' });
      }

      const locationData = {
        driverId: socket.userId,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        heading: heading || 0,
        speed: speed || 0,
        timestamp: new Date().toISOString()
      };

      // Store in memory and cache
      driverLocations.set(socket.userId, locationData);
      await cache.set(`driver_location_${socket.userId}`, locationData, 300); // 5 minutes

      // Broadcast to users tracking this driver
      socket.broadcast.emit('driver_location_updated', locationData);
      
      // Emit to specific ride if driver is on a trip
      const activeRide = await cache.get(`driver_active_ride_${socket.userId}`);
      if (activeRide) {
        io.to(`ride_${activeRide.rideId}`).emit('driver_location_updated', locationData);
      }
    });

    // Handle ride requests (from users)
    socket.on('request_ride', async (rideData) => {
      if (socket.userRole !== 'user') {
        return socket.emit('error', { message: 'Unauthorized: Only users can request rides' });
      }

      console.log(`Ride request from user ${socket.userId}:`, rideData);
      
      // Broadcast to nearby drivers
      socket.broadcast.to('role_driver').emit('new_ride_request', {
        rideId: rideData.rideId,
        userId: socket.userId,
        pickup: rideData.pickup,
        destination: rideData.destination,
        estimatedFare: rideData.estimatedFare,
        timestamp: new Date().toISOString()
      });

      // Store ride request in cache
      await cache.set(`ride_request_${rideData.rideId}`, {
        ...rideData,
        userId: socket.userId,
        status: 'pending'
      }, 600); // 10 minutes
    });

    // Handle ride acceptance (from drivers)
    socket.on('accept_ride', async (data) => {
      if (socket.userRole !== 'driver') {
        return socket.emit('error', { message: 'Unauthorized: Only drivers can accept rides' });
      }

      const { rideId } = data;
      const rideRequest = await cache.get(`ride_request_${rideId}`);
      
      if (!rideRequest) {
        return socket.emit('error', { message: 'Ride request not found or expired' });
      }

      if (rideRequest.status !== 'pending') {
        return socket.emit('error', { message: 'Ride already accepted by another driver' });
      }

      // Update ride status
      const updatedRide = {
        ...rideRequest,
        driverId: socket.userId,
        status: 'accepted',
        acceptedAt: new Date().toISOString()
      };

      await cache.set(`ride_request_${rideId}`, updatedRide, 3600); // 1 hour
      await cache.set(`driver_active_ride_${socket.userId}`, updatedRide, 3600);

      // Create ride room
      socket.join(`ride_${rideId}`);
      
      // Notify user
      io.to(`user_${rideRequest.userId}`).emit('ride_accepted', {
        rideId,
        driverId: socket.userId,
        driverLocation: driverLocations.get(socket.userId),
        message: 'Your ride has been accepted!'
      });

      // Notify other drivers that ride is no longer available
      socket.broadcast.to('role_driver').emit('ride_no_longer_available', { rideId });

      console.log(`Ride ${rideId} accepted by driver ${socket.userId}`);
    });

    // Handle ride status updates
    socket.on('update_ride_status', async (data) => {
      const { rideId, status } = data;
      
      if (!['started', 'arrived', 'completed', 'cancelled'].includes(status)) {
        return socket.emit('error', { message: 'Invalid ride status' });
      }

      const ride = await cache.get(`ride_request_${rideId}`);
      if (!ride) {
        return socket.emit('error', { message: 'Ride not found' });
      }

      // Update ride status
      const updatedRide = {
        ...ride,
        status,
        [`${status}At`]: new Date().toISOString()
      };

      await cache.set(`ride_request_${rideId}`, updatedRide, 3600);

      // Notify all participants in the ride
      io.to(`ride_${rideId}`).emit('ride_status_updated', {
        rideId,
        status,
        timestamp: new Date().toISOString()
      });

      // If ride is completed or cancelled, clean up
      if (['completed', 'cancelled'].includes(status)) {
        await cache.del(`driver_active_ride_${ride.driverId}`);
        io.in(`ride_${rideId}`).socketsLeave(`ride_${rideId}`);
      }

      console.log(`Ride ${rideId} status updated to: ${status}`);
    });

    // Handle user joining ride room (for tracking)
    socket.on('join_ride', (data) => {
      const { rideId } = data;
      socket.join(`ride_${rideId}`);
      console.log(`User ${socket.userId} joined ride ${rideId} for tracking`);
    });

    // Handle chat messages
    socket.on('send_message', async (data) => {
      const { rideId, message, type = 'text' } = data;
      
      const messageData = {
        senderId: socket.userId,
        senderRole: socket.userRole,
        message,
        type,
        timestamp: new Date().toISOString()
      };

      // Send to ride participants
      io.to(`ride_${rideId}`).emit('new_message', messageData);
      
      // Store in cache for message history
      const messagesKey = `ride_messages_${rideId}`;
      const existingMessages = await cache.get(messagesKey) || [];
      existingMessages.push(messageData);
      await cache.set(messagesKey, existingMessages, 86400); // 24 hours
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User ${socket.userId} (${socket.userRole}) disconnected`);
      
      // Remove from active connections
      activeConnections.delete(socket.userId);
      
      // If driver, remove location data
      if (socket.userRole === 'driver') {
        driverLocations.delete(socket.userId);
        cache.del(`driver_location_${socket.userId}`);
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error(`Socket error for user ${socket.userId}:`, error);
    });
  });

  // Periodic cleanup of expired data
  setInterval(async () => {
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    // Clean up old driver locations
    for (const [driverId, locationData] of driverLocations.entries()) {
      if (new Date(locationData.timestamp).getTime() < fiveMinutesAgo) {
        driverLocations.delete(driverId);
      }
    }
  }, 60000); // Run every minute
};

// Helper functions for external use
const getActiveConnections = () => activeConnections;
const getDriverLocations = () => driverLocations;

const emitToUser = (userId, event, data) => {
  const connection = activeConnections.get(userId);
  if (connection) {
    io.to(`user_${userId}`).emit(event, data);
    return true;
  }
  return false;
};

const emitToRole = (role, event, data) => {
  io.to(`role_${role}`).emit(event, data);
};

module.exports = socketHandler;
module.exports.getActiveConnections = getActiveConnections;
module.exports.getDriverLocations = getDriverLocations;
module.exports.emitToUser = emitToUser;
module.exports.emitToRole = emitToRole;
