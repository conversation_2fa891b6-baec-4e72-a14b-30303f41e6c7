{"name": "ride-hailing-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@googlemaps/react-wrapper": "^1.1.35", "@googlemaps/js-api-loader": "^1.16.2", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-select": "^5.8.0", "react-datepicker": "^4.25.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5"}}