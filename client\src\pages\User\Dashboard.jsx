const UserDashboard = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">User Dashboard</h1>
        <p className="text-gray-600">Book rides and track your trips</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-body">
            <h3 className="text-lg font-medium text-gray-900">Book a Ride</h3>
            <p className="text-gray-600 mt-2">Find and book rides to your destination</p>
            <button className="btn-primary mt-4">
              Book Now
            </button>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body">
            <h3 className="text-lg font-medium text-gray-900">Ride History</h3>
            <p className="text-gray-600 mt-2">View your past trips and receipts</p>
            <button className="btn-outline mt-4">
              View History
            </button>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body">
            <h3 className="text-lg font-medium text-gray-900">Profile</h3>
            <p className="text-gray-600 mt-2">Manage your account settings</p>
            <button className="btn-outline mt-4">
              Edit Profile
            </button>
          </div>
        </div>
      </div>
      
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
        </div>
        <div className="card-body">
          <p className="text-gray-500">No recent rides found.</p>
        </div>
      </div>
    </div>
  )
}

export default UserDashboard
