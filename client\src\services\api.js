import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('token')
      window.location.href = '/login'
      toast.error('Session expired. Please login again.')
    } else if (error.response?.status === 403) {
      // Forbidden
      toast.error('You do not have permission to perform this action.')
    } else if (error.response?.status === 404) {
      // Not found
      toast.error('Resource not found.')
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.')
    } else if (error.code === 'ECONNABORTED') {
      // Timeout
      toast.error('Request timeout. Please check your connection.')
    } else if (!error.response) {
      // Network error
      toast.error('Network error. Please check your connection.')
    }
    
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/api/auth/login', credentials),
  register: (userData) => api.post('/api/auth/register', userData),
  logout: () => api.post('/api/auth/logout'),
  verifyToken: () => api.get('/api/auth/verify'),
  updateProfile: (profileData) => api.put('/api/auth/profile', profileData),
  changePassword: (passwordData) => api.put('/api/auth/change-password', passwordData),
  forgotPassword: (email) => api.post('/api/auth/forgot-password', { email }),
  resetPassword: (token, newPassword) => api.post('/api/auth/reset-password', { token, newPassword }),
}

// User API
export const userAPI = {
  getProfile: () => api.get('/api/users/profile'),
  updateProfile: (profileData) => api.put('/api/users/profile', profileData),
  uploadAvatar: (formData) => api.post('/api/users/avatar', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteAccount: () => api.delete('/api/users/account'),
}

// Ride API
export const rideAPI = {
  // User ride operations
  requestRide: (rideData) => api.post('/api/rides/request', rideData),
  cancelRide: (rideId) => api.put(`/api/rides/${rideId}/cancel`),
  getRideHistory: (params) => api.get('/api/rides/history', { params }),
  getRideDetails: (rideId) => api.get(`/api/rides/${rideId}`),
  rateRide: (rideId, rating) => api.post(`/api/rides/${rideId}/rate`, rating),
  
  // Driver ride operations
  getAvailableRides: (params) => api.get('/api/rides/available', { params }),
  acceptRide: (rideId) => api.put(`/api/rides/${rideId}/accept`),
  startRide: (rideId) => api.put(`/api/rides/${rideId}/start`),
  completeRide: (rideId) => api.put(`/api/rides/${rideId}/complete`),
  updateLocation: (locationData) => api.post('/api/rides/location', locationData),
  
  // Common
  estimateFare: (routeData) => api.post('/api/rides/estimate-fare', routeData),
  getActiveRide: () => api.get('/api/rides/active'),
}

// Driver API
export const driverAPI = {
  getProfile: () => api.get('/api/drivers/profile'),
  updateProfile: (profileData) => api.put('/api/drivers/profile', profileData),
  updateStatus: (status) => api.put('/api/drivers/status', { status }),
  getEarnings: (params) => api.get('/api/drivers/earnings', { params }),
  getTrips: (params) => api.get('/api/drivers/trips', { params }),
  uploadDocuments: (formData) => api.post('/api/drivers/documents', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  getDocuments: () => api.get('/api/drivers/documents'),
}

// Admin API
export const adminAPI = {
  // Dashboard
  getDashboardStats: () => api.get('/api/admin/dashboard'),
  
  // User management
  getUsers: (params) => api.get('/api/admin/users', { params }),
  getUserDetails: (userId) => api.get(`/api/admin/users/${userId}`),
  updateUser: (userId, userData) => api.put(`/api/admin/users/${userId}`, userData),
  blockUser: (userId) => api.put(`/api/admin/users/${userId}/block`),
  unblockUser: (userId) => api.put(`/api/admin/users/${userId}/unblock`),
  deleteUser: (userId) => api.delete(`/api/admin/users/${userId}`),
  
  // Driver management
  getDrivers: (params) => api.get('/api/admin/drivers', { params }),
  getDriverDetails: (driverId) => api.get(`/api/admin/drivers/${driverId}`),
  approveDriver: (driverId) => api.put(`/api/admin/drivers/${driverId}/approve`),
  rejectDriver: (driverId) => api.put(`/api/admin/drivers/${driverId}/reject`),
  suspendDriver: (driverId) => api.put(`/api/admin/drivers/${driverId}/suspend`),
  
  // Ride management
  getAllRides: (params) => api.get('/api/admin/rides', { params }),
  getRideDetails: (rideId) => api.get(`/api/admin/rides/${rideId}`),
  
  // Reports
  getReports: (params) => api.get('/api/admin/reports', { params }),
}

// Owner API
export const ownerAPI = {
  // Dashboard
  getDashboardStats: () => api.get('/api/owner/dashboard'),
  
  // Reports
  getRevenueReports: (params) => api.get('/api/owner/reports/revenue', { params }),
  getUserReports: (params) => api.get('/api/owner/reports/users', { params }),
  getDriverReports: (params) => api.get('/api/owner/reports/drivers', { params }),
  getRideReports: (params) => api.get('/api/owner/reports/rides', { params }),
  
  // Admin management
  getAdmins: (params) => api.get('/api/owner/admins', { params }),
  createAdmin: (adminData) => api.post('/api/owner/admins', adminData),
  updateAdmin: (adminId, adminData) => api.put(`/api/owner/admins/${adminId}`, adminData),
  deleteAdmin: (adminId) => api.delete(`/api/owner/admins/${adminId}`),
  
  // Platform settings
  getSettings: () => api.get('/api/owner/settings'),
  updateSettings: (settings) => api.put('/api/owner/settings', settings),
}

// Payment API
export const paymentAPI = {
  createPaymentIntent: (amount, currency = 'usd') => api.post('/api/payments/create-intent', { amount, currency }),
  confirmPayment: (paymentIntentId) => api.post('/api/payments/confirm', { paymentIntentId }),
  getPaymentHistory: (params) => api.get('/api/payments/history', { params }),
  refundPayment: (paymentId, amount) => api.post('/api/payments/refund', { paymentId, amount }),
}

// Maps API (Google Maps integration)
export const mapsAPI = {
  geocode: (address) => api.post('/api/maps/geocode', { address }),
  reverseGeocode: (lat, lng) => api.post('/api/maps/reverse-geocode', { lat, lng }),
  getDirections: (origin, destination, waypoints = []) => 
    api.post('/api/maps/directions', { origin, destination, waypoints }),
  getDistanceMatrix: (origins, destinations) => 
    api.post('/api/maps/distance-matrix', { origins, destinations }),
  searchPlaces: (query, location) => api.post('/api/maps/places/search', { query, location }),
  getPlaceDetails: (placeId) => api.get(`/api/maps/places/${placeId}`),
}

// Notification API
export const notificationAPI = {
  getNotifications: (params) => api.get('/api/notifications', { params }),
  markAsRead: (notificationId) => api.put(`/api/notifications/${notificationId}/read`),
  markAllAsRead: () => api.put('/api/notifications/read-all'),
  deleteNotification: (notificationId) => api.delete(`/api/notifications/${notificationId}`),
  updatePushSubscription: (subscription) => api.post('/api/notifications/push-subscription', subscription),
}

export default api
