{"name": "ride-hailing-server", "version": "1.0.0", "description": "Backend API for ride-hailing platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pg": "^8.11.3", "redis": "^4.6.10", "sequelize": "^6.35.1", "socket.io": "^4.7.4", "sqlite3": "^5.1.7", "stripe": "^14.7.0", "twilio": "^4.19.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "keywords": ["api", "express", "nodejs", "ride-hailing", "real-time"], "author": "Your Name", "license": "MIT"}