{"name": "ride-hailing-server", "version": "1.0.0", "description": "Backend API for ride-hailing platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "pg": "^8.11.3", "sequelize": "^6.35.1", "redis": "^4.6.10", "socket.io": "^4.7.4", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "stripe": "^14.7.0", "nodemailer": "^6.9.7", "twilio": "^4.19.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2"}, "keywords": ["api", "express", "nodejs", "ride-hailing", "real-time"], "author": "Your Name", "license": "MIT"}