import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authAPI } from '../services/api'
import toast from 'react-hot-toast'

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authAPI.login(credentials)
          const { user, token } = response.data
          
          // Store token in localStorage for API calls
          localStorage.setItem('token', token)
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          
          toast.success(`Welcome back, ${user.firstName}!`)
          return { success: true, user }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Login failed'
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authAPI.register(userData)
          const { user, token } = response.data
          
          // Store token in localStorage for API calls
          localStorage.setItem('token', token)
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          
          toast.success(`Welcome to the platform, ${user.firstName}!`)
          return { success: true, user }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Registration failed'
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },

      logout: async () => {
        try {
          // Call logout API to invalidate token on server
          await authAPI.logout()
        } catch (error) {
          console.error('Logout API error:', error)
        } finally {
          // Clear local storage and state regardless of API call result
          localStorage.removeItem('token')
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
          
          toast.success('Logged out successfully')
        }
      },

      updateProfile: async (profileData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authAPI.updateProfile(profileData)
          const { user } = response.data
          
          set({
            user,
            isLoading: false,
            error: null
          })
          
          toast.success('Profile updated successfully')
          return { success: true, user }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Profile update failed'
          set({
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },

      changePassword: async (passwordData) => {
        set({ isLoading: true, error: null })
        
        try {
          await authAPI.changePassword(passwordData)
          
          set({
            isLoading: false,
            error: null
          })
          
          toast.success('Password changed successfully')
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Password change failed'
          set({
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },

      forgotPassword: async (email) => {
        set({ isLoading: true, error: null })
        
        try {
          await authAPI.forgotPassword(email)
          
          set({
            isLoading: false,
            error: null
          })
          
          toast.success('Password reset email sent')
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Failed to send reset email'
          set({
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },

      resetPassword: async (token, newPassword) => {
        set({ isLoading: true, error: null })
        
        try {
          await authAPI.resetPassword(token, newPassword)
          
          set({
            isLoading: false,
            error: null
          })
          
          toast.success('Password reset successfully')
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Password reset failed'
          set({
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },

      verifyToken: async () => {
        const token = localStorage.getItem('token')
        if (!token) {
          set({ isAuthenticated: false, user: null, token: null })
          return false
        }

        try {
          const response = await authAPI.verifyToken()
          const { user } = response.data
          
          set({
            user,
            token,
            isAuthenticated: true,
            error: null
          })
          
          return true
        } catch (error) {
          // Token is invalid, clear everything
          localStorage.removeItem('token')
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null
          })
          
          return false
        }
      },

      initializeAuth: async () => {
        const token = localStorage.getItem('token')
        if (token) {
          set({ token })
          await get().verifyToken()
        }
      },

      clearError: () => {
        set({ error: null })
      },

      // Utility getters
      isOwner: () => get().user?.role === 'owner',
      isAdmin: () => get().user?.role === 'admin',
      isDriver: () => get().user?.role === 'driver',
      isUser: () => get().user?.role === 'user',
      
      hasRole: (role) => get().user?.role === role,
      hasAnyRole: (roles) => roles.includes(get().user?.role),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

export { useAuthStore }
