import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './store/authStore'
import { useEffect } from 'react'

// Layout Components
import Layout from './components/Layout/Layout'
import AuthLayout from './components/Layout/AuthLayout'

// Auth Pages
import Login from './pages/Auth/Login'
import Register from './pages/Auth/Register'

// User Pages
import UserDashboard from './pages/User/Dashboard'
import BookRide from './pages/User/BookRide'
import RideHistory from './pages/User/RideHistory'
import UserProfile from './pages/User/Profile'

// Driver Pages
import DriverDashboard from './pages/Driver/Dashboard'
import DriverRides from './pages/Driver/Rides'
import DriverEarnings from './pages/Driver/Earnings'
import DriverProfile from './pages/Driver/Profile'

// Admin Pages
import AdminDashboard from './pages/Admin/Dashboard'
import ManageUsers from './pages/Admin/ManageUsers'
import ManageDrivers from './pages/Admin/ManageDrivers'
import ManageRides from './pages/Admin/ManageRides'

// Owner Pages
import OwnerDashboard from './pages/Owner/Dashboard'
import OwnerReports from './pages/Owner/Reports'
import OwnerSettings from './pages/Owner/Settings'

// Common Pages
import NotFound from './pages/NotFound'
import Unauthorized from './pages/Unauthorized'

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { user, isAuthenticated } = useAuthStore()
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }
  
  if (allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {
    return <Navigate to="/unauthorized" replace />
  }
  
  return children
}

// Role-based redirect component
const RoleBasedRedirect = () => {
  const { user, isAuthenticated } = useAuthStore()
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }
  
  switch (user?.role) {
    case 'user':
      return <Navigate to="/user/dashboard" replace />
    case 'driver':
      return <Navigate to="/driver/dashboard" replace />
    case 'admin':
      return <Navigate to="/admin/dashboard" replace />
    case 'owner':
      return <Navigate to="/owner/dashboard" replace />
    default:
      return <Navigate to="/login" replace />
  }
}

function App() {
  const { initializeAuth } = useAuthStore()
  
  useEffect(() => {
    initializeAuth()
  }, [initializeAuth])

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={
          <AuthLayout>
            <Login />
          </AuthLayout>
        } />
        <Route path="/register" element={
          <AuthLayout>
            <Register />
          </AuthLayout>
        } />
        
        {/* Root redirect */}
        <Route path="/" element={<RoleBasedRedirect />} />
        
        {/* User Routes */}
        <Route path="/user/*" element={
          <ProtectedRoute allowedRoles={['user']}>
            <Layout userRole="user">
              <Routes>
                <Route path="dashboard" element={<UserDashboard />} />
                <Route path="book-ride" element={<BookRide />} />
                <Route path="history" element={<RideHistory />} />
                <Route path="profile" element={<UserProfile />} />
                <Route path="*" element={<Navigate to="/user/dashboard" replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Driver Routes */}
        <Route path="/driver/*" element={
          <ProtectedRoute allowedRoles={['driver']}>
            <Layout userRole="driver">
              <Routes>
                <Route path="dashboard" element={<DriverDashboard />} />
                <Route path="rides" element={<DriverRides />} />
                <Route path="earnings" element={<DriverEarnings />} />
                <Route path="profile" element={<DriverProfile />} />
                <Route path="*" element={<Navigate to="/driver/dashboard" replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Admin Routes */}
        <Route path="/admin/*" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <Layout userRole="admin">
              <Routes>
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="users" element={<ManageUsers />} />
                <Route path="drivers" element={<ManageDrivers />} />
                <Route path="rides" element={<ManageRides />} />
                <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Owner Routes */}
        <Route path="/owner/*" element={
          <ProtectedRoute allowedRoles={['owner']}>
            <Layout userRole="owner">
              <Routes>
                <Route path="dashboard" element={<OwnerDashboard />} />
                <Route path="reports" element={<OwnerReports />} />
                <Route path="settings" element={<OwnerSettings />} />
                <Route path="*" element={<Navigate to="/owner/dashboard" replace />} />
              </Routes>
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Error Routes */}
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </div>
  )
}

export default App
