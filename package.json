{"name": "ride-hailing-platform", "version": "1.0.0", "description": "Full-stack ride-hailing solution with Owner, Admin, Driver, and User interfaces", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test"}, "keywords": ["ride-hailing", "taxi", "booking", "real-time", "maps", "nodejs", "react"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}