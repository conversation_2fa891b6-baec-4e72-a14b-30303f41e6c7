import { io } from 'socket.io-client'
import toast from 'react-hot-toast'

class SocketService {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.listeners = new Map()
  }

  connect(token) {
    if (this.socket?.connected) {
      return this.socket
    }

    const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3000'
    
    this.socket = io(socketUrl, {
      auth: {
        token: token || localStorage.getItem('token')
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    })

    this.setupEventListeners()
    return this.socket
  }

  setupEventListeners() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket.id)
      this.isConnected = true
      toast.success('Connected to real-time services')
    })

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      this.isConnected = false
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.socket.connect()
      }
    })

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      this.isConnected = false
      
      if (error.message === 'Authentication error: Invalid token') {
        toast.error('Authentication failed. Please login again.')
        localStorage.removeItem('token')
        window.location.href = '/login'
      } else {
        toast.error('Connection error. Retrying...')
      }
    })

    this.socket.on('error', (error) => {
      console.error('Socket error:', error)
      toast.error(error.message || 'Socket error occurred')
    })

    // Ride-related events
    this.socket.on('new_ride_request', (data) => {
      console.log('New ride request:', data)
      this.emit('new_ride_request', data)
    })

    this.socket.on('ride_accepted', (data) => {
      console.log('Ride accepted:', data)
      toast.success('Your ride has been accepted!')
      this.emit('ride_accepted', data)
    })

    this.socket.on('ride_status_updated', (data) => {
      console.log('Ride status updated:', data)
      this.emit('ride_status_updated', data)
    })

    this.socket.on('driver_location_updated', (data) => {
      this.emit('driver_location_updated', data)
    })

    this.socket.on('ride_no_longer_available', (data) => {
      console.log('Ride no longer available:', data)
      this.emit('ride_no_longer_available', data)
    })

    this.socket.on('new_message', (data) => {
      console.log('New message:', data)
      this.emit('new_message', data)
    })

    // Notification events
    this.socket.on('notification', (data) => {
      console.log('New notification:', data)
      toast.info(data.message)
      this.emit('notification', data)
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.isConnected = false
      this.listeners.clear()
    }
  }

  // Event emission methods
  updateDriverLocation(locationData) {
    if (this.socket?.connected) {
      this.socket.emit('driver_location_update', locationData)
    }
  }

  requestRide(rideData) {
    if (this.socket?.connected) {
      this.socket.emit('request_ride', rideData)
    }
  }

  acceptRide(rideId) {
    if (this.socket?.connected) {
      this.socket.emit('accept_ride', { rideId })
    }
  }

  updateRideStatus(rideId, status) {
    if (this.socket?.connected) {
      this.socket.emit('update_ride_status', { rideId, status })
    }
  }

  joinRide(rideId) {
    if (this.socket?.connected) {
      this.socket.emit('join_ride', { rideId })
    }
  }

  sendMessage(rideId, message, type = 'text') {
    if (this.socket?.connected) {
      this.socket.emit('send_message', { rideId, message, type })
    }
  }

  // Event listener management
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event).add(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback)
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in socket event listener for ${event}:`, error)
        }
      })
    }
  }

  // Utility methods
  isSocketConnected() {
    return this.socket?.connected || false
  }

  getSocketId() {
    return this.socket?.id || null
  }

  // Driver-specific methods
  goOnline() {
    if (this.socket?.connected) {
      this.socket.emit('driver_status_update', { status: 'online' })
    }
  }

  goOffline() {
    if (this.socket?.connected) {
      this.socket.emit('driver_status_update', { status: 'offline' })
    }
  }

  setBusy() {
    if (this.socket?.connected) {
      this.socket.emit('driver_status_update', { status: 'busy' })
    }
  }

  // Admin/Owner methods
  broadcastAnnouncement(message, targetRole = 'all') {
    if (this.socket?.connected) {
      this.socket.emit('broadcast_announcement', { message, targetRole })
    }
  }

  // Location tracking
  startLocationTracking(options = {}) {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser')
      return
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000,
      ...options
    }

    this.locationWatchId = navigator.geolocation.watchPosition(
      (position) => {
        const locationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          heading: position.coords.heading || 0,
          speed: position.coords.speed || 0,
          accuracy: position.coords.accuracy,
          timestamp: new Date().toISOString()
        }

        this.updateDriverLocation(locationData)
        this.emit('location_updated', locationData)
      },
      (error) => {
        console.error('Geolocation error:', error)
        toast.error('Failed to get location. Please check your permissions.')
      },
      defaultOptions
    )
  }

  stopLocationTracking() {
    if (this.locationWatchId) {
      navigator.geolocation.clearWatch(this.locationWatchId)
      this.locationWatchId = null
    }
  }

  // Get current location once
  getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'))
        return
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy
          })
        },
        (error) => {
          reject(error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      )
    })
  }
}

// Create singleton instance
const socketService = new SocketService()

export default socketService
