# Ride-Hailing Platform

A comprehensive full-stack ride-hailing solution with four user types: Owner, Admin, Driver, and User.

## 🚀 Features

### 👑 Owner Dashboard
- Platform management and oversight
- Revenue and earnings reports
- Analytics and usage statistics
- Admin management

### 👨‍💻 Admin Panel
- Driver management (add/edit/remove)
- User management and support
- Booking oversight
- Complaints and feedback handling

### 🚗 Driver App
- Ride request acceptance/rejection
- Real-time navigation with Google Maps
- Trip management (start/end)
- Earnings and wallet dashboard
- User ratings and reviews

### 👤 User App
- Easy ride booking with map interface
- Real-time driver tracking
- Fare estimation
- Ride history
- Multiple payment options
- Driver rating system

## 🛠️ Tech Stack

- **Frontend**: React.js with Vite
- **Backend**: Node.js with Express
- **Database**: PostgreSQL with Redis for caching
- **Real-time**: Socket.IO
- **Maps**: Google Maps API
- **Authentication**: JWT with role-based access
- **Payments**: Stripe/Razorpay integration

## 📁 Project Structure

```
ride-hailing-platform/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── hooks/          # Custom React hooks
│   │   ├── utils/          # Utility functions
│   │   └── styles/         # CSS/styling
├── server/                 # Node.js backend
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── middleware/     # Custom middleware
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
├── database/               # Database scripts
└── docs/                   # Documentation
```

## 🚀 Quick Start

1. **Install dependencies**:
   ```bash
   npm run install:all
   ```

2. **Set up environment variables**:
   - Copy `.env.example` to `.env` in both client and server directories
   - Add your Google Maps API key, database credentials, etc.

3. **Start development servers**:
   ```bash
   npm run dev
   ```

4. **Access the application**:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000

## 🔧 Environment Setup

### Required Environment Variables

#### Server (.env)
```
NODE_ENV=development
PORT=3000
DATABASE_URL=postgresql://username:password@localhost:5432/ride_hailing
JWT_SECRET=your-jwt-secret
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
REDIS_URL=redis://localhost:6379
STRIPE_SECRET_KEY=your-stripe-secret-key
```

#### Client (.env)
```
VITE_API_URL=http://localhost:3000
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
VITE_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
```

## 📊 Database Schema

The application uses PostgreSQL with the following main entities:
- Users (Owner, Admin, Driver, User)
- Rides/Bookings
- Vehicles
- Payments
- Reviews/Ratings

## 🔐 Authentication & Authorization

Role-based access control with JWT tokens:
- **Owner**: Full platform access
- **Admin**: User and driver management
- **Driver**: Ride management and tracking
- **User**: Booking and tracking rides

## 🗺️ Google Maps Integration

- **Places API**: Location autocomplete
- **Directions API**: Route planning
- **Distance Matrix API**: Fare calculation
- **Geolocation API**: Real-time tracking
- **Maps SDK**: Interactive maps

## 📱 Real-time Features

- Live driver location tracking
- Instant ride status updates
- Real-time notifications
- Chat between driver and user

## 💳 Payment Integration

- Multiple payment methods (UPI, Cards, Wallet)
- Secure payment processing
- Driver earnings management
- Automated fare calculation

## 🧪 Testing

```bash
npm test
```

## 📦 Deployment

The application can be deployed on:
- **Frontend**: Vercel, Netlify
- **Backend**: Railway, Render, AWS
- **Database**: PostgreSQL on Railway, AWS RDS

## 📄 License

MIT License - see LICENSE file for details.
